namespace Q.FilterBuilder.IntegrationTests.Configuration;

/// <summary>
/// Configuration provider for integration tests
/// </summary>
public class TestConfiguration
{
    private readonly IConfiguration _configuration;

    public TestConfiguration(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    /// <summary>
    /// Gets the database provider to use for testing
    /// </summary>
    public DatabaseProvider GetDatabaseProvider()
    {
        var providerName = _configuration["DatabaseProvider"] ??
                          Environment.GetEnvironmentVariable("DatabaseProvider") ??
                          "SqlServer";

        if (Enum.TryParse<DatabaseProvider>(providerName, true, out var provider))
        {
            return provider;
        }

        throw new InvalidOperationException($"Invalid database provider: {providerName}. Supported providers: {string.Join(", ", Enum.GetNames<DatabaseProvider>())}");
    }

    /// <summary>
    /// Gets whether to use Docker containers for databases
    /// </summary>
    public bool UseDocker()
    {
        return _configuration.GetValue("UseDocker", true);
    }

    /// <summary>
    /// Gets the connection string for a specific provider (when not using Docker)
    /// </summary>
    public string? GetConnectionString(DatabaseProvider provider)
    {
        return provider switch
        {
            DatabaseProvider.SqlServer => _configuration.GetConnectionString("SqlServer"),
            DatabaseProvider.MySql => _configuration.GetConnectionString("MySql"),
            DatabaseProvider.PostgreSql => _configuration.GetConnectionString("PostgreSql"),
            _ => null
        };
    }

    /// <summary>
    /// Gets the list of providers to test against
    /// </summary>
    public DatabaseProvider[] GetProvidersToTest()
    {
        var providersConfig = _configuration["ProvidersToTest"];
        if (string.IsNullOrEmpty(providersConfig))
        {
            // Default to all providers
            return Enum.GetValues<DatabaseProvider>();
        }

        return providersConfig
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(p => Enum.Parse<DatabaseProvider>(p.Trim(), ignoreCase: true))
            .ToArray();
    }

    /// <summary>
    /// Gets whether to run tests in parallel across providers
    /// </summary>
    public bool RunInParallel()
    {
        return _configuration.GetValue("RunInParallel", false);
    }
}
