using Q.FilterBuilder.IntegrationTests.Configuration;
using Testcontainers.PostgreSql;
using Npgsql;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Containers;

/// <summary>
/// PostgreSQL container strategy implementation
/// </summary>
public class PostgreSqlContainerStrategy : IContainerStrategy
{
    private PostgreSqlContainer? _container;

    public DatabaseProvider Provider => DatabaseProvider.PostgreSql;

    public async Task<string> InitializeAsync(DatabaseConfiguration configuration)
    {
        var builder = new PostgreSqlBuilder()
            .WithImage(configuration.Container.ImageName)
            .WithDatabase(configuration.Database)
            .WithUsername(configuration.Username)
            .WithPassword(configuration.Password)
            .WithCleanUp(true);

        // Add environment variables
        foreach (var env in configuration.Container.Environment)
        {
            builder = builder.WithEnvironment(env.Key, env.Value);
        }

        _container = builder.Build();
        await _container.StartAsync();
        
        return _container.GetConnectionString();
    }

    public async Task DisposeAsync()
    {
        if (_container != null)
        {
            await _container.DisposeAsync();
            _container = null;
        }
    }

    public async Task<bool> IsHealthyAsync()
    {
        if (_container == null)
        {
            return false;
        }

        try
        {
            // Try to execute a simple query to check health
            var connectionString = _container.GetConnectionString();
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
