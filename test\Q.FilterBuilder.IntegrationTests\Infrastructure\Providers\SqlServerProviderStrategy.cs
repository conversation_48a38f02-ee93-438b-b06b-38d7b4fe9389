using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;
using Q.FilterBuilder.SqlServer.Extensions;
using Testcontainers.MsSql;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Providers;

/// <summary>
/// SQL Server provider strategy implementation
/// </summary>
public class SqlServerProviderStrategy : IProviderStrategy
{
    private MsSqlContainer? _container;

    public DatabaseProvider Provider => DatabaseProvider.SqlServer;

    public void ConfigureFilterBuilder(IServiceCollection services)
    {
        services.AddSqlServerFilterBuilder();
    }

    public void ConfigureDbContext(DbContextOptionsBuilder options, string connectionString)
    {
        options.UseSqlServer(connectionString);
    }

    public string GetConnectionString(IConfiguration configuration)
    {
        return configuration.GetConnectionString("SqlServer") ??
               throw new InvalidOperationException("SQL Server connection string not found");
    }

    public string BuildConnectionString(DatabaseConfiguration databaseConfig)
    {
        var connectionString = $"Server={databaseConfig.Server},{databaseConfig.Port};Database={databaseConfig.Database};User Id={databaseConfig.Username};Password={databaseConfig.Password};";
        if (databaseConfig.TrustServerCertificate)
        {
            connectionString += "TrustServerCertificate=true;";
        }
        return connectionString;
    }

    public TestDbContext CreateDbContext(string connectionString)
    {
        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseSqlServer(connectionString)
            .Options;

        return new TestDbContext(options);
    }

    public async Task<bool> ValidateProviderAsync(string connectionString)
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public ProviderTestConfiguration GetTestConfiguration()
    {
        return new ProviderTestConfiguration
        {
            DisplayName = "SQL Server",
            RequiresContainer = true,
            AdditionalProperties = new Dictionary<string, object>
            {
                ["DefaultPort"] = 1433,
                ["ImageName"] = "mcr.microsoft.com/mssql/server:2022-latest",
                ["AcceptEula"] = true
            }
        };
    }

    public async Task<string> InitializeContainerAsync(DatabaseConfiguration configuration)
    {
        var builder = new MsSqlBuilder()
            .WithImage(configuration.Container.ImageName)
            .WithPassword(configuration.Password)
            .WithCleanUp(true);

        // Add environment variables
        foreach (var env in configuration.Container.Environment)
        {
            builder = builder.WithEnvironment(env.Key, env.Value);
        }

        _container = builder.Build();
        await _container.StartAsync();

        return _container.GetConnectionString();
    }

    public async Task DisposeContainerAsync()
    {
        if (_container != null)
        {
            await _container.DisposeAsync();
            _container = null;
        }
    }

    public async Task<bool> IsContainerHealthyAsync()
    {
        if (_container == null)
        {
            return false;
        }

        try
        {
            // Try to execute a simple query to check health
            var connectionString = _container.GetConnectionString();
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
