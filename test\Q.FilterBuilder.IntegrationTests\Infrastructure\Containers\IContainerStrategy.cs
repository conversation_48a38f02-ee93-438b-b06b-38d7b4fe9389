using Q.FilterBuilder.IntegrationTests.Configuration;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Containers;

/// <summary>
/// Strategy interface for managing database containers
/// </summary>
public interface IContainerStrategy
{
    /// <summary>
    /// The database provider this strategy handles
    /// </summary>
    DatabaseProvider Provider { get; }

    /// <summary>
    /// Initialize and start the database container
    /// </summary>
    /// <param name="configuration">Database configuration</param>
    /// <returns>Connection string for the started container</returns>
    Task<string> InitializeAsync(DatabaseConfiguration configuration);

    /// <summary>
    /// Stop and dispose the database container
    /// </summary>
    Task DisposeAsync();

    /// <summary>
    /// Check if the container is running and healthy
    /// </summary>
    Task<bool> IsHealthyAsync();
}
