{"DatabaseProvider": "SqlServer", "DatabaseConfiguration": {"SqlServer": {"Server": "localhost", "Port": 1433, "Database": "FilterBuilderTest", "Username": "sa", "Password": "YourStrong@Passw0rd123", "TrustServerCertificate": true, "Container": {"ImageName": "mcr.microsoft.com/mssql/server:2022-latest", "Environment": {"ACCEPT_EULA": "Y", "MSSQL_PID": "Express"}}}, "MySql": {"Server": "localhost", "Port": 3306, "Database": "testdb", "Username": "testuser", "Password": "testpass", "Container": {"ImageName": "mysql:8.0", "Environment": {}}}, "PostgreSql": {"Server": "localhost", "Port": 5432, "Database": "testdb", "Username": "testuser", "Password": "testpass", "Container": {"ImageName": "postgres:15", "Environment": {}}}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}}