{"DatabaseProvider": "SqlServer", "SqlServerConfiguration": {"Container": {"ImageName": "mcr.microsoft.com/mssql/server:2022-latest", "Database": "FilterBuilderTest", "Username": "sa", "Password": "YourStrong@Passw0rd123", "Environment": {"ACCEPT_EULA": "Y", "MSSQL_PID": "Express"}}}, "MySqlConfiguration": {"Container": {"ImageName": "mysql:8.0", "Database": "testdb", "Username": "testuser", "Password": "testpass", "Environment": {"MYSQL_ROOT_PASSWORD": "testpass", "MYSQL_DATABASE": "testdb", "MYSQL_USER": "testuser", "MYSQL_PASSWORD": "testpass"}}}, "PostgreSqlConfiguration": {"Container": {"ImageName": "postgres:15", "Database": "testdb", "Username": "testuser", "Password": "testpass", "Environment": {"POSTGRES_DB": "testdb", "POSTGRES_USER": "testuser", "POSTGRES_PASSWORD": "testpass"}}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}}