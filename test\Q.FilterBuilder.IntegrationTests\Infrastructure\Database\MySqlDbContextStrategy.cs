using Microsoft.EntityFrameworkCore;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Database;

/// <summary>
/// MySQL database context strategy implementation
/// </summary>
public class MySqlDbContextStrategy : IDbContextStrategy
{
    public DatabaseProvider Provider => DatabaseProvider.MySql;

    public TestDbContext CreateContext(string connectionString)
    {
        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseMySql(connectionString, ServerVersion.AutoDetect(connectionString))
            .Options;

        return new TestDbContext(options);
    }

    public void ConfigureDbContext(DbContextOptionsBuilder<TestDbContext> options, string connectionString)
    {
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
    }
}
