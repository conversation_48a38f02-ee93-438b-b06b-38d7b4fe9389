using Q.FilterBuilder.IntegrationTests.Configuration;
using Testcontainers.MsSql;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Containers;

/// <summary>
/// SQL Server container strategy implementation
/// </summary>
public class SqlServerContainerStrategy : IContainerStrategy
{
    private MsSqlContainer? _container;

    public DatabaseProvider Provider => DatabaseProvider.SqlServer;

    public async Task<string> InitializeAsync(DatabaseConfiguration configuration)
    {
        var builder = new MsSqlBuilder()
            .WithImage(configuration.Container.ImageName)
            .WithPassword(configuration.Password)
            .WithCleanUp(true);

        // Add environment variables
        foreach (var env in configuration.Container.Environment)
        {
            builder = builder.WithEnvironment(env.Key, env.Value);
        }

        _container = builder.Build();
        await _container.StartAsync();
        
        return _container.GetConnectionString();
    }

    public async Task DisposeAsync()
    {
        if (_container != null)
        {
            await _container.DisposeAsync();
            _container = null;
        }
    }

    public async Task<bool> IsHealthyAsync()
    {
        if (_container == null)
        {
            return false;
        }

        try
        {
            // Try to execute a simple query to check health
            var connectionString = _container.GetConnectionString();
            using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
