using Microsoft.EntityFrameworkCore;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;
using Q.FilterBuilder.PostgreSql.Extensions;
using Testcontainers.PostgreSql;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Providers;

/// <summary>
/// PostgreSQL provider strategy implementation
/// </summary>
public class PostgreSqlProviderStrategy : IProviderStrategy
{
    private PostgreSqlContainer? _container;

    public DatabaseProvider Provider => DatabaseProvider.PostgreSql;

    public void ConfigureFilterBuilder(IServiceCollection services)
    {
        services.AddPostgreSqlFilterBuilder();
    }

    public void ConfigureDbContext(DbContextOptionsBuilder options, string connectionString)
    {
        options.UseNpgsql(connectionString);
    }

    public string BuildConnectionString(DatabaseConfiguration databaseConfig)
    {
        return $"Host={databaseConfig.Server};Port={databaseConfig.Port};Database={databaseConfig.Database};Username={databaseConfig.Username};Password={databaseConfig.Password};";
    }

    public TestDbContext CreateDbContext(string connectionString)
    {
        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseNpgsql(connectionString)
            .Options;

        return new TestDbContext(options);
    }



    public async Task<string> InitializeContainerAsync(DatabaseConfiguration configuration)
    {
        var builder = new PostgreSqlBuilder()
            .WithImage(configuration.Container.ImageName)
            .WithDatabase(configuration.Database)
            .WithUsername(configuration.Username)
            .WithPassword(configuration.Password)
            .WithCleanUp(true);

        // Add environment variables
        foreach (var env in configuration.Container.Environment)
        {
            builder = builder.WithEnvironment(env.Key, env.Value);
        }

        _container = builder.Build();
        await _container.StartAsync();

        return _container.GetConnectionString();
    }

    public async Task DisposeContainerAsync()
    {
        if (_container != null)
        {
            await _container.DisposeAsync();
            _container = null;
        }
    }
}
