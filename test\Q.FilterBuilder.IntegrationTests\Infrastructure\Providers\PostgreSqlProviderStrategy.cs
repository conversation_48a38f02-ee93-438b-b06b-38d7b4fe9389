using Microsoft.EntityFrameworkCore;
using Npgsql;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.PostgreSql.Extensions;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Providers;

/// <summary>
/// PostgreSQL provider strategy implementation
/// </summary>
public class PostgreSqlProviderStrategy : IProviderStrategy
{
    public DatabaseProvider Provider => DatabaseProvider.PostgreSql;

    public void ConfigureFilterBuilder(IServiceCollection services)
    {
        services.AddPostgreSqlFilterBuilder();
    }

    public void ConfigureDbContext(DbContextOptionsBuilder options, string connectionString)
    {
        options.UseNpgsql(connectionString);
    }

    public string GetConnectionString(IConfiguration configuration)
    {
        return configuration.GetConnectionString("TestDatabase") ?? 
               throw new InvalidOperationException("PostgreSQL connection string not found");
    }

    public async Task<bool> ValidateProviderAsync(string connectionString)
    {
        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public ProviderTestConfiguration GetTestConfiguration()
    {
        return new ProviderTestConfiguration
        {
            DisplayName = "PostgreSQL",
            RequiresContainer = true,
            StartupTimeout = TimeSpan.FromMinutes(2),
            AdditionalProperties = new Dictionary<string, object>
            {
                ["DefaultPort"] = 5432,
                ["ImageName"] = "postgres:15",
                ["Database"] = "testdb",
                ["Username"] = "testuser",
                ["Password"] = "TestPassword123!"
            }
        };
    }
}
