namespace Q.FilterBuilder.IntegrationTests.Configuration;

/// <summary>
/// Configuration model for database provider settings
/// </summary>
public class DatabaseConfiguration
{
    public string Server { get; set; } = string.Empty;
    public int Port { get; set; }
    public string Database { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool TrustServerCertificate { get; set; }
    public ContainerConfiguration Container { get; set; } = new();
}

/// <summary>
/// Configuration model for container settings
/// </summary>
public class ContainerConfiguration
{
    public string ImageName { get; set; } = string.Empty;
    public Dictionary<string, string> Environment { get; set; } = new();
}

/// <summary>
/// Root configuration model for all database providers
/// </summary>
public class DatabaseConfigurationRoot
{
    public DatabaseConfiguration SqlServer { get; set; } = new();
    public DatabaseConfiguration MySql { get; set; } = new();
    public DatabaseConfiguration PostgreSql { get; set; } = new();

    /// <summary>
    /// Gets the configuration for the specified provider
    /// </summary>
    public DatabaseConfiguration GetConfiguration(DatabaseProvider provider)
    {
        return provider switch
        {
            DatabaseProvider.SqlServer => SqlServer,
            DatabaseProvider.MySql => MySql,
            DatabaseProvider.PostgreSql => PostgreSql,
            _ => throw new NotSupportedException($"Provider {provider} is not supported")
        };
    }
}
