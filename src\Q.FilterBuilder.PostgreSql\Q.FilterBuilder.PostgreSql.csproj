﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="../Q.FilterBuilder.Core/Q.FilterBuilder.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <AssemblyName>Q.FilterBuilder.PostgreSql</AssemblyName>
    <RootNamespace>Q.FilterBuilder.PostgreSql</RootNamespace>
    <PackageId>Q.FilterBuilder.PostgreSql</PackageId>
  </PropertyGroup>

</Project>
