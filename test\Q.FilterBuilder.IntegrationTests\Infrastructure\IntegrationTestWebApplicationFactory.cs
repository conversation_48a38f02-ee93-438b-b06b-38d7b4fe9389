using Microsoft.AspNetCore.Mvc.Testing;
using Q.FilterBuilder.IntegrationTests.Configuration;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure;

/// <summary>
/// Web application factory for integration tests
/// </summary>
public class IntegrationTestWebApplicationFactory : WebApplicationFactory<Program>
{
    private DatabaseProvider _provider;
    private string _connectionString = string.Empty;

    public void ConfigureProvider(DatabaseProvider provider, string connectionString)
    {
        _provider = provider;
        _connectionString = connectionString;
    }

    // protected override void ConfigureWebHost(IWebHostBuilder builder)
    // {
    //     builder.ConfigureAppConfiguration((context, config) =>
    //     {
    //         // Load the base configuration from appsettings.test.json
    //         config.AddJsonFile("appsettings.test.json", optional: false);

    //         // Override specific values for the test
    //         config.AddInMemoryCollection(new Dictionary<string, string?>
    //         {
    //             ["DatabaseProvider"] = _provider.ToString(),
    //             ["ConnectionStrings:TestDatabase"] = _connectionString
    //         });
    //     });

    //     builder.UseEnvironment("Testing");
    // }
}
