using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;
using Q.FilterBuilder.MySql.Extensions;
using Testcontainers.MySql;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Providers;

/// <summary>
/// MySQL provider strategy implementation
/// </summary>
public class MySqlProviderStrategy : IProviderStrategy
{
    private MySqlContainer? _container;

    public DatabaseProvider Provider => DatabaseProvider.MySql;

    public void ConfigureFilterBuilder(IServiceCollection services)
    {
        services.AddMySqlFilterBuilder();
    }

    public void ConfigureDbContext(DbContextOptionsBuilder options, string connectionString)
    {
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
    }

    public string GetConnectionString(IConfiguration configuration)
    {
        return configuration.GetConnectionString("MySql") ??
               throw new InvalidOperationException("MySQL connection string not found");
    }

    public string BuildConnectionString(DatabaseConfiguration databaseConfig)
    {
        return $"Server={databaseConfig.Server};Port={databaseConfig.Port};Database={databaseConfig.Database};Uid={databaseConfig.Username};Pwd={databaseConfig.Password};";
    }

    public TestDbContext CreateDbContext(string connectionString)
    {
        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseMySql(connectionString, ServerVersion.AutoDetect(connectionString))
            .Options;

        return new TestDbContext(options);
    }

    public async Task<bool> ValidateProviderAsync(string connectionString)
    {
        try
        {
            using var connection = new MySqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public ProviderTestConfiguration GetTestConfiguration()
    {
        return new ProviderTestConfiguration
        {
            DisplayName = "MySQL",
            RequiresContainer = true,
            AdditionalProperties = new Dictionary<string, object>
            {
                ["DefaultPort"] = 3306,
                ["ImageName"] = "mysql:8.0",
                ["RootPassword"] = "TestPassword123!"
            }
        };
    }

    public async Task<string> InitializeContainerAsync(DatabaseConfiguration configuration)
    {
        var builder = new MySqlBuilder()
            .WithImage(configuration.Container.ImageName)
            .WithDatabase(configuration.Database)
            .WithUsername(configuration.Username)
            .WithPassword(configuration.Password)
            .WithCleanUp(true);

        // Add environment variables
        foreach (var env in configuration.Container.Environment)
        {
            builder = builder.WithEnvironment(env.Key, env.Value);
        }

        _container = builder.Build();
        await _container.StartAsync();

        return _container.GetConnectionString();
    }

    public async Task DisposeContainerAsync()
    {
        if (_container != null)
        {
            await _container.DisposeAsync();
            _container = null;
        }
    }

    public async Task<bool> IsContainerHealthyAsync()
    {
        if (_container == null)
        {
            return false;
        }

        try
        {
            // Try to execute a simple query to check health
            var connectionString = _container.GetConnectionString();
            using var connection = new MySqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
