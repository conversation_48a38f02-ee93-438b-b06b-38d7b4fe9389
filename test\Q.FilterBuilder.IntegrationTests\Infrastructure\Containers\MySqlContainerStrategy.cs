using Q.FilterBuilder.IntegrationTests.Configuration;
using Testcontainers.MySql;
using MySqlConnector;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Containers;

/// <summary>
/// MySQL container strategy implementation
/// </summary>
public class MySqlContainerStrategy : IContainerStrategy
{
    private MySqlContainer? _container;

    public DatabaseProvider Provider => DatabaseProvider.MySql;

    public async Task<string> InitializeAsync(DatabaseConfiguration configuration)
    {
        var builder = new MySqlBuilder()
            .WithImage(configuration.Container.ImageName)
            .WithDatabase(configuration.Database)
            .WithUsername(configuration.Username)
            .WithPassword(configuration.Password)
            .WithCleanUp(true);

        // Add environment variables
        foreach (var env in configuration.Container.Environment)
        {
            builder = builder.WithEnvironment(env.Key, env.Value);
        }

        _container = builder.Build();
        await _container.StartAsync();
        
        return _container.GetConnectionString();
    }

    public async Task DisposeAsync()
    {
        if (_container != null)
        {
            await _container.DisposeAsync();
            _container = null;
        }
    }

    public async Task<bool> IsHealthyAsync()
    {
        if (_container == null)
        {
            return false;
        }

        try
        {
            // Try to execute a simple query to check health
            var connectionString = _container.GetConnectionString();
            using var connection = new MySqlConnection(connectionString);
            await connection.OpenAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
