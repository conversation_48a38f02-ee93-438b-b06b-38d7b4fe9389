using Q.FilterBuilder.IntegrationTests.Configuration;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Containers;

/// <summary>
/// Factory for creating container strategy instances
/// </summary>
public class ContainerStrategyFactory
{
    private readonly Dictionary<DatabaseProvider, Func<IContainerStrategy>> _strategyFactories;

    public ContainerStrategyFactory()
    {
        _strategyFactories = new Dictionary<DatabaseProvider, Func<IContainerStrategy>>
        {
            [DatabaseProvider.SqlServer] = () => new SqlServerContainerStrategy(),
            [DatabaseProvider.MySql] = () => new MySqlContainerStrategy(),
            [DatabaseProvider.PostgreSql] = () => new PostgreSqlContainerStrategy()
        };
    }

    /// <summary>
    /// Create a container strategy for the specified provider
    /// </summary>
    /// <param name="provider">Database provider</param>
    /// <returns>Container strategy instance</returns>
    /// <exception cref="NotSupportedException">Thrown when provider is not supported</exception>
    public IContainerStrategy CreateStrategy(DatabaseProvider provider)
    {
        if (_strategyFactories.TryGetValue(provider, out var factory))
        {
            return factory();
        }

        throw new NotSupportedException($"Container strategy for provider {provider} is not supported");
    }

    /// <summary>
    /// Check if a provider is supported
    /// </summary>
    /// <param name="provider">Database provider to check</param>
    /// <returns>True if provider is supported</returns>
    public bool IsProviderSupported(DatabaseProvider provider)
    {
        return _strategyFactories.ContainsKey(provider);
    }

    /// <summary>
    /// Get all supported providers
    /// </summary>
    /// <returns>Collection of supported providers</returns>
    public IEnumerable<DatabaseProvider> GetSupportedProviders()
    {
        return _strategyFactories.Keys;
    }
}
