using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Infrastructure.Containers;
using Xunit;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure;

/// <summary>
/// Shared database container fixture that creates one container per provider for all tests
/// </summary>
public class DatabaseContainerFixture : IAsyncLifetime
{
    private IContainerStrategy? _containerStrategy;
    private readonly IConfiguration _configuration;
    private readonly DatabaseConfigurationRoot _databaseConfig;

    public DatabaseProvider Provider { get; private set; }
    public string ConnectionString { get; private set; } = string.Empty;

    public DatabaseContainerFixture()
    {
        // Load configuration from appsettings.test.json
        _configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.test.json", optional: false)
            .AddEnvironmentVariables()
            .Build();

        // Bind database configuration
        _databaseConfig = new DatabaseConfigurationRoot();
        _configuration.GetSection("DatabaseConfiguration").Bind(_databaseConfig);

        var testConfig = new TestConfiguration(_configuration);
        Provider = testConfig.GetDatabaseProvider();
    }

    public async Task InitializeAsync()
    {
        // Create container strategy for the provider
        var containerFactory = new ContainerStrategyFactory();
        _containerStrategy = containerFactory.CreateStrategy(Provider);

        // Get database configuration for the provider
        var databaseConfig = _databaseConfig.GetConfiguration(Provider);

        // Initialize container and get connection string
        ConnectionString = await _containerStrategy.InitializeAsync(databaseConfig);
    }

    public async Task DisposeAsync()
    {
        if (_containerStrategy != null)
        {
            await _containerStrategy.DisposeAsync();
        }
    }
}

/// <summary>
/// Test collection definition to share the database container across all test classes
/// </summary>
[CollectionDefinition("DatabaseCollection")]
public class DatabaseCollection : ICollectionFixture<DatabaseContainerFixture>
{
    // This class has no code, and is never created. Its purpose is simply
    // to be the place to apply [CollectionDefinition] and all the
    // ICollectionFixture<> interfaces.
}
