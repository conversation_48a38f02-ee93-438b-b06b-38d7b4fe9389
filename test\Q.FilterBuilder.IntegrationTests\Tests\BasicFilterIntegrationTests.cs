using System.Text.Json;
using Q.FilterBuilder.IntegrationTests.Infrastructure;
using Xunit;

namespace Q.FilterBuilder.IntegrationTests.Tests;

/// <summary>
/// Basic integration tests for FilterBuilder functionality
/// </summary>
public class BasicFilterIntegrationTests : IntegrationTestBase
{
    public BasicFilterIntegrationTests(IntegrationTestWebApplicationFactory factory, DatabaseContainerFixture containerFixture)
        : base(factory, containerFixture)
    {
    }

    [Fact]
    public async Task BasicStringFilter_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Name",
                    "operator": "equal",
                    "value": "John Doe",
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        Assert.Contains("John <PERSON>", result);
    }

    [Fact]
    public async Task NumericComparisonFilter_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Age",
                    "operator": "greater_or_equal",
                    "value": 30,
                    "type": "int"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        // Should return users with age >= 30 (John Doe: 30, Bob Johnson: 35, Alice Brown: 32)
        Assert.Contains("John Doe", result);
        Assert.Contains("Bob Johnson", result);
        Assert.Contains("Alice Brown", result);
    }

    [Fact]
    public async Task BooleanFilter_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "IsActive",
                    "operator": "equal",
                    "value": true,
                    "type": "bool"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        // Should return only active users (John Doe, Jane Smith, Alice Brown)
        Assert.Contains("John Doe", result);
        Assert.Contains("Jane Smith", result);
        Assert.Contains("Alice Brown", result);
        Assert.DoesNotContain("Bob Johnson", result); // Bob is inactive
    }

    [Fact]
    public async Task ArrayInFilter_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Department",
                    "operator": "in",
                    "value": ["Technology", "Marketing"],
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        // Should return users in Technology or Marketing departments
        Assert.Contains("John Doe", result);    // Technology
        Assert.Contains("Jane Smith", result);  // Marketing
        Assert.Contains("Alice Brown", result); // Technology
        Assert.DoesNotContain("Bob Johnson", result); // Finance
    }

    [Fact]
    public async Task NullCheckFilter_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "LastLoginDate",
                    "operator": "is_not_null",
                    "value": null
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        // Should return users who have logged in (all except Bob Johnson)
        Assert.Contains("John Doe", result);
        Assert.Contains("Jane Smith", result);
        Assert.Contains("Alice Brown", result);
        Assert.DoesNotContain("Bob Johnson", result); // LastLoginDate is null
    }

    [Fact]
    public async Task DateTimeFilter_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "CreatedDate",
                    "operator": "greater_or_equal",
                    "value": "2023-02-01T00:00:00Z",
                    "type": "datetime"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        // Should return users created after Feb 1, 2023 (Jane Smith, Bob Johnson, Alice Brown)
        Assert.Contains("Jane Smith", result);  // Created 2023-02-01
        Assert.Contains("Bob Johnson", result); // Created 2023-03-01
        Assert.Contains("Alice Brown", result); // Created 2023-04-01
        Assert.DoesNotContain("John Doe", result); // Created 2023-01-15
    }

    [Fact]
    public async Task StringContainsFilter_ShouldReturnCorrectResults()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Email",
                    "operator": "contains",
                    "value": "@company.com",
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        if (!response.IsSuccessStatusCode)
        {
            var errorContent = await response.Content.ReadAsStringAsync();
            throw new Exception($"Request failed with status {response.StatusCode}: {errorContent}");
        }

        var result = await response.Content.ReadAsStringAsync();
        Assert.NotNull(result);
        // Should return all users since they all have @company.com emails
        Assert.Contains("John Doe", result);
        Assert.Contains("Jane Smith", result);
        Assert.Contains("Bob Johnson", result);
        Assert.Contains("Alice Brown", result);
    }

    [Fact]
    public async Task BuildQuery_ShouldReturnQueryAndParameters()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Name",
                    "operator": "equal",
                    "value": "John Doe",
                    "type": "string"
                },
                {
                    "field": "Age",
                    "operator": "greater",
                    "value": 25,
                    "type": "int"
                }
            ]
        }
        """);

        // Act
        var queryResult = await ExecuteFilterForQueryAsync(filterJson);

        // Assert
        Assert.NotNull(queryResult);
        Assert.NotEmpty(queryResult.Query);
        Assert.NotEmpty(queryResult.Parameters);
        Assert.Equal(2, queryResult.Parameters.Length);
        Assert.Contains("Name", queryResult.Query);
        Assert.Contains("Age", queryResult.Query);
    }
}
