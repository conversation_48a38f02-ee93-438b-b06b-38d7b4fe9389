{"Version": 1, "WorkspaceRootPath": "C:\\git-repos\\DynamicWhereBuilder\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\databasecontainerfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\databasecontainerfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\integrationtestbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\integrationtestbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\mysqlproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\mysqlproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\iproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\iproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\postgresqlproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\postgresqlproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\getting_started.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\getting_started.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\developer_guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\developer_guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\providerstrategyfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\providerstrategyfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\endtoendworkflowtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\endtoendworkflowtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\basicfilterintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\basicfilterintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\appsettings.test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\appsettings.test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\sqlserverproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\sqlserverproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\database\\testdataseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\database\\testdataseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\integrationtestwebapplicationfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\integrationtestwebapplicationfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\database\\testdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\database\\testdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\complexfilterintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\complexfilterintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\controllers\\integrationtestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\controllers\\integrationtestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\teststartup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\teststartup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\configuration\\testconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\configuration\\testconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\configuration\\databaseprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\configuration\\databaseprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 5, "Title": "GETTING_STARTED.md", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\GETTING_STARTED.md", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\GETTING_STARTED.md", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\GETTING_STARTED.md", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\GETTING_STARTED.md", "ViewState": "AgIAANgAAAAAAAAAAAAAAOsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-11T02:41:49.689Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ProviderStrategyFactory.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\ProviderStrategyFactory.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\ProviderStrategyFactory.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\ProviderStrategyFactory.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\ProviderStrategyFactory.cs", "ViewState": "AgIAACwAAAAAAAAAAAApwBYAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:38:05.498Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "DatabaseContainerFixture.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\DatabaseContainerFixture.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\DatabaseContainerFixture.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\DatabaseContainerFixture.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\DatabaseContainerFixture.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAQwCEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:33:08.523Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "IntegrationTestBase.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestBase.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestBase.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestBase.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestBase.cs", "ViewState": "AgIAAD0AAAAAAAAAAAAYwEYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:03:17.191Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "PostgreSqlProviderStrategy.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\PostgreSqlProviderStrategy.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\PostgreSqlProviderStrategy.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\PostgreSqlProviderStrategy.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\PostgreSqlProviderStrategy.cs", "ViewState": "AgIAADcAAAAAAAAAAAAQwEYAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:37:29.624Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IProviderStrategy.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\IProviderStrategy.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\IProviderStrategy.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\IProviderStrategy.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\IProviderStrategy.cs", "ViewState": "AgIAACcAAAAAAAAAAAAjwDUAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:36:09.568Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MySqlProviderStrategy.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\MySqlProviderStrategy.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\MySqlProviderStrategy.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\MySqlProviderStrategy.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\MySqlProviderStrategy.cs", "ViewState": "AgIAADgAAAAAAAAAAADgv0cAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T05:23:21.431Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "EndToEndWorkflowTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\EndToEndWorkflowTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\EndToEndWorkflowTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\EndToEndWorkflowTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\EndToEndWorkflowTests.cs", "ViewState": "AgIAABUBAAAAAAAAAAAAwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:06:14.254Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "BasicFilterIntegrationTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\BasicFilterIntegrationTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\BasicFilterIntegrationTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\BasicFilterIntegrationTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\BasicFilterIntegrationTests.cs", "ViewState": "AgIAAMUAAAAAAAAAAADgv9wAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:02:52.394Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "appsettings.test.json", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-10T05:12:33.089Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "README.md", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\README.md", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\README.md", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\README.md", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\README.md", "ViewState": "AgIAAFgAAAAAAAAAAAAQwPkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-10T05:01:54.683Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "DEVELOPER_GUIDE.md", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\DEVELOPER_GUIDE.md", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\DEVELOPER_GUIDE.md", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\DEVELOPER_GUIDE.md", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\DEVELOPER_GUIDE.md", "ViewState": "AgIAAMkAAAAAAAAAAAAAAOAAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-10T05:01:54.857Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "TestDataSeeder.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDataSeeder.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDataSeeder.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDataSeeder.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDataSeeder.cs", "ViewState": "AgIAABoAAAAAAAAAAAASwB8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:53:39.005Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "IntegrationTestWebApplicationFactory.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestWebApplicationFactory.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestWebApplicationFactory.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestWebApplicationFactory.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestWebApplicationFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:34:36.407Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "TestDbContext.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDbContext.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDbContext.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDbContext.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDbContext.cs", "ViewState": "AgIAAKUAAAAAAAAAAAAmwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:35:54.136Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "SqlServerProviderStrategy.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\SqlServerProviderStrategy.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\SqlServerProviderStrategy.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\SqlServerProviderStrategy.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\SqlServerProviderStrategy.cs", "ViewState": "AgIAAAsAAAAAAAAAAAA1wEkAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:36:30.687Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "ComplexFilterIntegrationTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexFilterIntegrationTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexFilterIntegrationTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexFilterIntegrationTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexFilterIntegrationTests.cs", "ViewState": "AgIAANUAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:05:35.808Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "IntegrationTestController.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAgwIgBAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:49:33.041Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "TestStartup.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\TestStartup.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\TestStartup.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\TestStartup.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\TestStartup.cs", "ViewState": "AgIAACAAAAAAAAAAAAAwwDsAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:31:15.551Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "DatabaseProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Configuration\\DatabaseProvider.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Configuration\\DatabaseProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Configuration\\DatabaseProvider.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Configuration\\DatabaseProvider.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:30:19.96Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "TestConfiguration.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Configuration\\TestConfiguration.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Configuration\\TestConfiguration.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Configuration\\TestConfiguration.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Configuration\\TestConfiguration.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAcwAsAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:29:21.304Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "Program.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Program.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Program.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Program.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:28:39.648Z", "EditorCaption": ""}]}]}]}