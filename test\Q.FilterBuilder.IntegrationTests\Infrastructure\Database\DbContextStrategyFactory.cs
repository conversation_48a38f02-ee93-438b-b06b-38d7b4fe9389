using Q.FilterBuilder.IntegrationTests.Configuration;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Database;

/// <summary>
/// Factory for creating database context strategy instances
/// </summary>
public class DbContextStrategyFactory
{
    private readonly Dictionary<DatabaseProvider, Func<IDbContextStrategy>> _strategyFactories;

    public DbContextStrategyFactory()
    {
        _strategyFactories = new Dictionary<DatabaseProvider, Func<IDbContextStrategy>>
        {
            [DatabaseProvider.SqlServer] = () => new SqlServerDbContextStrategy(),
            [DatabaseProvider.MySql] = () => new MySqlDbContextStrategy(),
            [DatabaseProvider.PostgreSql] = () => new PostgreSqlDbContextStrategy()
        };
    }

    /// <summary>
    /// Create a database context strategy for the specified provider
    /// </summary>
    /// <param name="provider">Database provider</param>
    /// <returns>Database context strategy instance</returns>
    /// <exception cref="NotSupportedException">Thrown when provider is not supported</exception>
    public IDbContextStrategy CreateStrategy(DatabaseProvider provider)
    {
        if (_strategyFactories.TryGetValue(provider, out var factory))
        {
            return factory();
        }

        throw new NotSupportedException($"Database context strategy for provider {provider} is not supported");
    }

    /// <summary>
    /// Check if a provider is supported
    /// </summary>
    /// <param name="provider">Database provider to check</param>
    /// <returns>True if provider is supported</returns>
    public bool IsProviderSupported(DatabaseProvider provider)
    {
        return _strategyFactories.ContainsKey(provider);
    }

    /// <summary>
    /// Get all supported providers
    /// </summary>
    /// <returns>Collection of supported providers</returns>
    public IEnumerable<DatabaseProvider> GetSupportedProviders()
    {
        return _strategyFactories.Keys;
    }
}
