using Microsoft.EntityFrameworkCore;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Database;

/// <summary>
/// SQL Server database context strategy implementation
/// </summary>
public class SqlServerDbContextStrategy : IDbContextStrategy
{
    public DatabaseProvider Provider => DatabaseProvider.SqlServer;

    public TestDbContext CreateContext(string connectionString)
    {
        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseSqlServer(connectionString)
            .Options;

        return new TestDbContext(options);
    }

    public void ConfigureDbContext(DbContextOptionsBuilder<TestDbContext> options, string connectionString)
    {
        options.UseSqlServer(connectionString);
    }
}
