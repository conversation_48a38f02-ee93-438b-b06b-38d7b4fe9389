using System.Text.Json;
using Q.FilterBuilder.IntegrationTests.Infrastructure;
using Xunit;

namespace Q.FilterBuilder.IntegrationTests.Tests;

/// <summary>
/// End-to-end workflow tests that demonstrate the complete FilterBuilder integration
/// from JSON input through query building to database execution
/// </summary>
public class EndToEndWorkflowTests : IntegrationTestBase
{
    public EndToEndWorkflowTests(IntegrationTestWebApplicationFactory factory, DatabaseContainerFixture containerFixture)
        : base(factory, containerFixture)
    {
    }

    [Fact]
    public async Task CompleteWorkflow_JsonToDatabase_ShouldExecuteSuccessfully()
    {
        // Arrange - Simulate JSON from a frontend query builder
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "IsActive",
                    "operator": "equal",
                    "value": true,
                    "type": "bool"
                },
                {
                    "field": "Department",
                    "operator": "in",
                    "value": ["Technology", "Marketing"],
                    "type": "string"
                }
            ],
            "groups": [
                {
                    "condition": "OR",
                    "rules": [
                        {
                            "field": "Age",
                            "operator": "greater",
                            "value": 30,
                            "type": "int"
                        },
                        {
                            "field": "Salary",
                            "operator": "greater",
                            "value": 70000,
                            "type": "decimal"
                        }
                    ]
                }
            ]
        }
        """);

        // Act - Execute the complete workflow
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert - Verify the complete workflow succeeded
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();

        Assert.NotNull(content);
        Assert.Contains("query", content, StringComparison.OrdinalIgnoreCase);
        Assert.Contains("parameters", content, StringComparison.OrdinalIgnoreCase);

        // Should return John Doe (Tech, Age 30, Salary 75000, Active) and Alice Brown (Tech, Age 32, Salary 70000, Active)
        Assert.Contains("John Doe", content);
        Assert.Contains("Alice Brown", content);
    }

    [Fact]
    public async Task QueryBuilding_ShouldGenerateCorrectSqlForProvider()
    {
        // Arrange
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Name",
                    "operator": "contains",
                    "value": "John",
                    "type": "string"
                },
                {
                    "field": "Age",
                    "operator": "between",
                    "value": [25, 35],
                    "type": "int"
                }
            ]
        }
        """);

        // Act
        var queryResult = await ExecuteFilterForQueryAsync(filterJson);

        // Assert
        Assert.NotNull(queryResult);
        Assert.NotEmpty(queryResult.Query);
        Assert.Equal(3, queryResult.Parameters.Length); // 1 for contains + 2 for between
        
        // Verify the query contains expected elements
        Assert.Contains("Name", queryResult.Query);
        Assert.Contains("Age", queryResult.Query);
        Assert.Contains("BETWEEN", queryResult.Query.ToUpper());
        
        // Verify parameters
        Assert.Equal("John", queryResult.Parameters[0]?.ToString()?.Trim());

        // Handle JsonElement parameters
        var param1 = queryResult.Parameters[1];
        var param2 = queryResult.Parameters[2];

        if (param1 is JsonElement elem1)
        {
            Assert.Equal(25, elem1.GetInt32());
        }
        else
        {
            Assert.Equal(25, Convert.ToInt32(param1));
        }

        if (param2 is JsonElement elem2)
        {
            Assert.Equal(35, elem2.GetInt32());
        }
        else
        {
            Assert.Equal(35, Convert.ToInt32(param2));
        }
    }

    [Fact]
    public async Task TypeConversion_ShouldHandleAllDataTypes()
    {
        // Arrange - Test all major data types
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "Name",
                    "operator": "equal",
                    "value": "John Doe",
                    "type": "string"
                },
                {
                    "field": "Age",
                    "operator": "equal",
                    "value": 30,
                    "type": "int"
                },
                {
                    "field": "Salary",
                    "operator": "equal",
                    "value": 75000.00,
                    "type": "decimal"
                },
                {
                    "field": "IsActive",
                    "operator": "equal",
                    "value": true,
                    "type": "bool"
                },
                {
                    "field": "CreatedDate",
                    "operator": "equal",
                    "value": "2023-01-15T00:00:00Z",
                    "type": "datetime"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        
        Assert.NotNull(result);
        Assert.Contains("John Doe", result); // Should match exactly one user
        
        // Verify the result contains expected data
        Assert.Contains("\"count\":1", result); // Should return exactly 1 user
    }

    [Fact]
    public async Task ErrorHandling_ShouldReturnMeaningfulErrors()
    {
        // Arrange - Invalid filter with non-existent field
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "NonExistentField",
                    "operator": "equal",
                    "value": "test",
                    "type": "string"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        // The response might be successful but return an error in the content
        // or it might return a bad request - either is acceptable
        var content = await response.Content.ReadAsStringAsync();
        Assert.NotNull(content);
        
        // If it's a bad request, it should contain error information
        if (!response.IsSuccessStatusCode)
        {
            Assert.Contains("error", content, StringComparison.OrdinalIgnoreCase);
        }
    }

    [Fact]
    public async Task HealthCheck_ShouldReturnSystemStatus()
    {
        // Act
        var response = await Client.GetAsync("/api/IntegrationTest/health");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        
        Assert.NotNull(content);
        Assert.Contains("Healthy", content);
        Assert.Contains(Provider.ToString(), content);
    }

    [Fact]
    public async Task ProductsWorkflow_ShouldFilterProductsCorrectly()
    {
        // Arrange - Filter for available products under $500
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "AND",
            "rules": [
                {
                    "field": "IsAvailable",
                    "operator": "equal",
                    "value": true,
                    "type": "bool"
                },
                {
                    "field": "Price",
                    "operator": "less",
                    "value": 500,
                    "type": "decimal"
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-products-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        
        Assert.NotNull(result);
        
        // Should return Marketing Suite (299.99) and Code Editor Pro (99.99)
        Assert.Contains("Marketing Suite", result);
        Assert.Contains("Code Editor Pro", result);
        
        // Should not return Laptop Pro (1299.99) or Financial Dashboard (not available)
        Assert.DoesNotContain("Laptop Pro", result);
        Assert.DoesNotContain("Financial Dashboard", result);
    }

    [Fact]
    public async Task ComplexNestedQuery_ShouldExecuteCorrectly()
    {
        // Arrange - Complex nested query with multiple conditions
        var filterJson = JsonDocument.Parse("""
        {
            "condition": "OR",
            "rules": [
                {
                    "condition": "AND",
                    "rules": [
                        {
                            "field": "Department",
                            "operator": "equal",
                            "value": "Technology",
                            "type": "string"
                        },
                        {
                            "field": "IsActive",
                            "operator": "equal",
                            "value": true,
                            "type": "bool"
                        }
                    ]
                },
                {
                    "condition": "AND",
                    "rules": [
                        {
                            "field": "Role",
                            "operator": "equal",
                            "value": "Manager",
                            "type": "string"
                        },
                        {
                            "field": "Salary",
                            "operator": "greater",
                            "value": 60000,
                            "type": "decimal"
                        }
                    ]
                }
            ]
        }
        """);

        // Act
        var response = await Client.PostAsJsonAsync("/api/IntegrationTest/execute-users-filter", filterJson);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsStringAsync();
        
        Assert.NotNull(result);
        
        // Should return active Technology users (John Doe, Alice Brown) and Managers with salary > 60000 (Jane Smith)
        Assert.Contains("John Doe", result);    // Technology, Active
        Assert.Contains("Alice Brown", result); // Technology, Active
        Assert.Contains("Jane Smith", result);  // Manager, Salary 65000
        
        // Bob Johnson should not be included (Finance, not active, not a manager with high salary)
        Assert.DoesNotContain("Bob Johnson", result);
    }
}
