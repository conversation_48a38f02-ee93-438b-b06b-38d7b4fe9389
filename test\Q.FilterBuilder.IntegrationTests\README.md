# Q.FilterBuilder.IntegrationTests

Comprehensive integration test project for Q.FilterBuilder that validates the complete workflow from JSON input to actual database query execution using **Testcontainers** and **ASP.NET Core Test Server**.

## Overview

This test project validates the **complete real-world workflow** via web API endpoints:

1. **JSON Input** → **QueryBuilderConverter** → **FilterGroup**
2. **FilterGroup** → **IFilterBuilder** → **SQL Query + Parameters**
3. **Query Execution** → **Multiple ORMs** → **Database Results**
4. **Result Validation** → **Assert Expected Outcomes**

## Project Structure

```
Q.FilterBuilder.IntegrationTests/
├── Configuration/         # Provider configuration and settings
│   ├── DatabaseConfiguration.cs
│   ├── DatabaseProvider.cs
│   └── TestConfiguration.cs
├── Controllers/           # ASP.NET Core test controllers
│   └── IntegrationTestController.cs
├── Database/             # Entity models and data seeding
│   ├── Models/
│   │   ├── User.cs
│   │   ├── Category.cs
│   │   └── Product.cs
│   ├── TestDbContext.cs
│   └── TestDataSeeder.cs
├── Infrastructure/       # Test infrastructure and providers
│   ├── Providers/        # Database provider strategies
│   │   ├── IProviderStrategy.cs
│   │   ├── BaseProviderStrategy.cs
│   │   ├── SqlServerProviderStrategy.cs
│   │   ├── MySqlProviderStrategy.cs
│   │   ├── PostgreSqlProviderStrategy.cs
│   │   └── ProviderStrategyFactory.cs
│   ├── DatabaseContainerFixture.cs
│   ├── IntegrationTestBase.cs
│   ├── IntegrationTestWebApplicationFactory.cs
│   ├── JsonTestDataLoader.cs
│   └── TestStartup.cs
├── Tests/                # Integration test classes
│   ├── BasicFilterIntegrationTests.cs
│   └── EndToEndWorkflowTests.cs
├── JsonSamples/          # Sample JSON payloads
│   ├── basic-string-filter.json
│   ├── complex-nested-filter.json
│   ├── email-contains-filter.json
│   ├── name-age-filter.json
│   └── name-contains-age-between-filter.json
├── appsettings.test.json # Test configuration
└── README.md
```

## Testcontainers Integration

The integration tests use **Testcontainers for .NET** to automatically manage database containers:

### Supported Databases
- **SQL Server** - `mcr.microsoft.com/mssql/server:2022-latest`
- **MySQL** - `mysql:8.0`
- **PostgreSQL** - `postgres:15`

### Automatic Container Management
- Containers are **automatically started** before each test class
- Containers are **automatically cleaned up** after each test class
- Each test class gets **fresh database instances**
- No manual Docker setup required

### Test Data
Each container is automatically populated with:
- **Users table** - 4 test users with various attributes
- **Categories table** - 3 product categories  
- **Products table** - 4 test products linked to categories

### Prerequisites
- Docker Desktop must be running
- .NET 8.0 SDK
- No additional setup required - Testcontainers handles everything

## Provider Strategy Pattern

The test infrastructure uses a strategy pattern to handle different database providers:

### Provider Strategy Architecture

The test infrastructure uses a hierarchical strategy pattern with a base class containing common functionality:

#### **BaseProviderStrategy<TContainer, TBuilder>**
Abstract base class that provides common container management functionality:
- **Container Lifecycle Management**: Initialize, start, and dispose containers
- **Common Configuration Flow**: Standardized container setup process
- **Template Method Pattern**: Defines the algorithm, delegates specifics to derived classes
- **Type Safety**: Generic constraints ensure proper container and builder types
- **Dynamic Method Calls**: Uses dynamic typing to call common methods on all Testcontainer types
- **Maximum Code Reuse**: Only provider-specific configuration needs to be implemented

#### **IProviderStrategy Interface**
Each provider strategy implements:
- **ConfigureFilterBuilder()**: Register provider-specific FilterBuilder services
- **ConfigureDbContext()**: Configure Entity Framework for the provider
- **CreateDbContext()**: Create configured DbContext instances
- **InitializeContainerAsync()**: Start and configure Docker containers (returns connection string)
- **DisposeContainerAsync()**: Clean up containers after tests

#### **Provider Implementations**
- **SqlServerProviderStrategy**: SQL Server with Testcontainers.MsSql
- **MySqlProviderStrategy**: MySQL with Testcontainers.MySql
- **PostgreSqlProviderStrategy**: PostgreSQL with Testcontainers.PostgreSql

Each provider only implements the truly provider-specific logic:
- **CreateContainerBuilder()**: Create the appropriate builder type
- **ConfigureContainerBuilder()**: Configure builder with database-specific settings
- **ConfigureFilterBuilder()**: Register provider-specific FilterBuilder services
- **ConfigureDbContext()**: Configure Entity Framework for the provider

All common operations are handled by the base class using dynamic method calls:
- **AddEnvironmentVariables()**: Calls `WithEnvironment()` on any builder
- **BuildContainer()**: Calls `Build()` on any builder
- **StartContainerAsync()**: Calls `StartAsync()` on any container
- **GetConnectionString()**: Calls `GetConnectionString()` on any container

### Key Features
- **Automatic Container Management**: Containers start/stop automatically
- **Provider-Specific Parameter Handling**: Handles different parameter formats
- **Multi-ORM Testing**: Validates EF Core, Dapper, and ADO.NET compatibility
- **Real Database Execution**: Tests against actual database instances
- **Comprehensive Error Handling**: Detailed error context and cleanup
- **Simplified Configuration**: Container-based configuration with automatic connection string generation
- **DRY Architecture**: Base provider strategy eliminates code duplication
- **Type-Safe Generics**: Compile-time safety for container and builder types
- **Template Method Pattern**: Consistent container lifecycle across all providers

### Configuration Architecture

The test infrastructure uses an extremely simplified configuration approach:

1. **Provider Selection**: The `DatabaseProvider` setting determines which provider to use
2. **Direct Provider Sections**: Each provider has its own flat configuration section (e.g., `SqlServer`, `MySql`, `PostgreSql`)
3. **Flat Configuration Structure**: All settings are at the top level of each provider section
4. **Automatic Connection Strings**: Connection strings are generated automatically by Testcontainers
5. **Simple Configuration Binding**: Uses `Configuration.GetSection(provider.ToString()).Bind(databaseConfig)`
6. **No Nested Objects**: Eliminated unnecessary nesting for maximum simplicity

## Test Classes

### BasicFilterIntegrationTests
**Basic integration tests covering fundamental operations:**
- String equality and contains operations
- Numeric comparisons (greater than, between)
- Boolean filtering
- Array operations (in, not_in)
- Null checks (is_null, is_not_null)
- DateTime filtering
- Query building validation

### EndToEndWorkflowTests
**Complete workflow integration tests:**
- Complex nested filter groups with AND/OR logic
- Multi-condition queries across all data types
- Type conversion validation
- Error handling and validation scenarios
- Health checks and system status
- Cross-table filtering (Users and Products)
- Provider-specific parameter handling
- Multi-ORM execution comparison (EF, Dapper, ADO.NET)

## Running Tests

### Run All Tests

```bash
# Run all integration tests
dotnet test test/Q.FilterBuilder.IntegrationTests/
```

### Run Specific Provider Tests

Tests automatically run against all available providers. To test a specific provider:

```bash
# SQL Server only
$env:TEST_DATABASE_PROVIDER="SqlServer"
dotnet test test/Q.FilterBuilder.IntegrationTests/

# MySQL only
$env:TEST_DATABASE_PROVIDER="MySql"
dotnet test test/Q.FilterBuilder.IntegrationTests/

# PostgreSQL only
$env:TEST_DATABASE_PROVIDER="PostgreSql"
dotnet test test/Q.FilterBuilder.IntegrationTests/
```

### Run Specific Test Categories

```bash
# Basic integration tests only
dotnet test --filter "BasicFilterIntegrationTests"

# End-to-end workflow tests only
dotnet test --filter "EndToEndWorkflowTests"

# Specific test method
dotnet test --filter "CompleteWorkflow_JsonToDatabase_ShouldExecuteSuccessfully"
```

### Using Visual Studio
1. Ensure Docker Desktop is running
2. Open Test Explorer
3. Run tests individually or by category

## Configuration

### Environment Variables
- `TEST_DATABASE_PROVIDER` - Database provider to use (SqlServer, MySql, PostgreSql)

### appsettings.test.json
```json
{
  "DatabaseProvider": "SqlServer",
  "SqlServer": {
    "ImageName": "mcr.microsoft.com/mssql/server:2022-latest",
    "Database": "FilterBuilderTest",
    "Username": "sa",
    "Password": "YourStrong@Passw0rd123",
    "Environment": {
      "ACCEPT_EULA": "Y",
      "MSSQL_PID": "Express"
    }
  },
  "MySql": {
    "ImageName": "mysql:8.0",
    "Database": "testdb",
    "Username": "testuser",
    "Password": "testpass",
  },
  "PostgreSql": {
    "ImageName": "postgres:15",
    "Database": "testdb",
    "Username": "testuser",
    "Password": "testpass"
  }
}
```

## Test Data Schema

### Users Table
- **Id** (int, PK)
- **Name** (string, required)
- **Email** (string, required)
- **Age** (int)
- **Salary** (decimal)
- **IsActive** (bool)
- **CreatedDate** (datetime)
- **LastLoginDate** (datetime, nullable)
- **Department** (string, nullable)
- **Role** (string, nullable)
- **CategoryId** (int, FK, nullable)

### Categories Table
- **Id** (int, PK)
- **Name** (string, required)
- **Description** (string, nullable)
- **IsActive** (bool)
- **CreatedDate** (datetime)

### Products Table
- **Id** (int, PK)
- **Name** (string, required)
- **Description** (string, nullable)
- **Price** (decimal)
- **Stock** (int)
- **IsAvailable** (bool)
- **CreatedDate** (datetime)
- **UpdatedDate** (datetime, nullable)
- **Status** (string, nullable)
- **Rating** (double, nullable)
- **CategoryId** (int, FK)
- **CreatedByUserId** (int, FK, nullable)
- **Tags** (string, JSON array)
- **Metadata** (string, JSON object)

## ORM Testing

Each test validates query execution across multiple ORMs:

### Entity Framework Core
- Uses `FromSqlRaw` for parameterized queries
- Validates EF Core compatibility

### Dapper
- Direct SQL execution with dynamic parameters
- Validates micro-ORM compatibility

### ADO.NET
- Raw database connection and command execution
- Validates low-level database compatibility

## Troubleshooting

### Common Issues and Solutions

**Configuration Path Issues:**
- **Problem**: `appsettings.test.json` not found during test execution
- **Solution**: Ensure `IntegrationTestWebApplicationFactory` properly configures content root path
- **Fix**: Use `FindProjectRoot()` method to locate project directory

**JSON Test Data Loading:**
- **Problem**: JSON sample files not found during test execution
- **Solution**: `JsonTestDataLoader` searches up directory tree to find JsonSamples folder
- **Fix**: Implement dynamic path resolution in constructor

**Database Table Case Sensitivity:**
- **Problem**: PostgreSQL table name case sensitivity issues (`"Users"` vs `users`)
- **Solution**: Use provider-specific table name quoting in queries
- **Fix**: Implement `GetTableName()` method with provider-specific formatting

**Parameter Binding Errors:**
- **Problem**: PostgreSQL parameters (`$1`, `$2`) incompatible with Dapper (`@p0`, `@p1`)
- **Solution**: Convert parameter formats for each provider
- **Fix**: Implement parameter mapping logic in `ExecuteQueryAsync()`

**Container Startup Failures:**
- **Problem**: Docker containers fail to start or become ready
- **Solution**: Ensure Docker Desktop is running with sufficient resources
- **Fix**: Implement proper health checks and wait strategies

### Docker Issues
```bash
# Check Docker status
docker info

# Pull required images manually
docker pull mcr.microsoft.com/mssql/server:2022-latest
docker pull mysql:8.0
docker pull postgres:15

# Check container logs if tests fail
docker logs <container_id>
```

### Debug Mode
Enable detailed logging:
```bash
$env:LOGGING__LOGLEVEL__DEFAULT="Debug"
$env:LOGGING__LOGLEVEL__MICROSOFT="Information"
dotnet test test/Q.FilterBuilder.IntegrationTests/ --verbosity detailed
```

### Performance Considerations
- Container startup adds ~10-15 seconds per provider
- Tests run sequentially to avoid resource conflicts
- Database seeding occurs once per container lifecycle
- Connection pooling disabled for clean test isolation

## Contributing

When adding new tests:
1. Follow the existing naming conventions
2. Include comprehensive assertions
3. Test across all supported providers
4. Update JSON samples as needed
5. Document expected behavior clearly
