using Microsoft.EntityFrameworkCore;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Database;

/// <summary>
/// PostgreSQL database context strategy implementation
/// </summary>
public class PostgreSqlDbContextStrategy : IDbContextStrategy
{
    public DatabaseProvider Provider => DatabaseProvider.PostgreSql;

    public TestDbContext CreateContext(string connectionString)
    {
        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseNpgsql(connectionString)
            .Options;

        return new TestDbContext(options);
    }

    public void ConfigureDbContext(DbContextOptionsBuilder<TestDbContext> options, string connectionString)
    {
        options.UseNpgsql(connectionString);
    }
}
