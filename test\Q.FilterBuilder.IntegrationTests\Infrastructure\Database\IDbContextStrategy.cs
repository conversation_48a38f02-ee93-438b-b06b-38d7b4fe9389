using Microsoft.EntityFrameworkCore;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;

namespace Q.FilterBuilder.IntegrationTests.Infrastructure.Database;

/// <summary>
/// Strategy interface for creating database contexts
/// </summary>
public interface IDbContextStrategy
{
    /// <summary>
    /// The database provider this strategy handles
    /// </summary>
    DatabaseProvider Provider { get; }

    /// <summary>
    /// Create a database context with the specified connection string
    /// </summary>
    /// <param name="connectionString">Database connection string</param>
    /// <returns>Configured TestDbContext instance</returns>
    TestDbContext CreateContext(string connectionString);

    /// <summary>
    /// Configure Entity Framework DbContext options for this provider
    /// </summary>
    /// <param name="options">DbContext options builder</param>
    /// <param name="connectionString">Database connection string</param>
    void ConfigureDbContext(DbContextOptionsBuilder<TestDbContext> options, string connectionString);
}
